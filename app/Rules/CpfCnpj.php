<?php

declare(strict_types=1);

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CpfCnpj implements ValidationRule
{
    private Cpf $cpfRule;
    private Cnpj $cnpjRule;

    /**
     * Create a new rule instance.
     */
    public function __construct()
    {
        $this->cpfRule = new Cpf();
        $this->cnpjRule = new Cnpj();
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->passes($attribute, $value)) {
            $fail($this->message());
        }
    }

    /**
     * Determine if the validation rule passes.
     */
    public function passes(string $attribute, mixed $value): bool
    {
        // Handle null and empty values
        if (empty($value)) {
            return false;
        }

        $document = preg_replace('/[^0-9]/is', '', $value);
        $length = strlen($document);

        if ($length === 11) {
            return $this->cpfRule->passes($attribute, $document);
        }

        if ($length === 14) {
            return $this->cnpjRule->passes($attribute, $document);
        }

        return false;
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return 'O documento informado não é um CPF ou CNPJ válido.';
    }
}
