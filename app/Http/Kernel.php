<?php

namespace App\Http;

use App\Http\Middleware\AddAcceptJsonHeader;
use App\Http\Middleware\ApiMopFiltersGestor;
use App\Http\Middleware\CheckBolsaoAccess;
use App\Http\Middleware\CheckCorretorAprovado;
use App\Http\Middleware\CheckIpService;
use App\Http\Middleware\CheckPerfilGestor;
use App\Http\Middleware\CheckSalesforceToken;
use App\Http\Middleware\CheckServiceEmailCms;
use App\Http\Middleware\CheckServiceRole;
use App\Http\Middleware\BankIsEnabled;
use App\Http\Middleware\Contas\CheckClienteContaCliente;
use App\Http\Middleware\EditUserGestorPerfil;
use App\Http\Middleware\EnsureGestorHasPermission;
use App\Http\Middleware\HasActiveBankAccount;
use App\Http\Middleware\HasBankAccount;
use App\Http\Middleware\HasBankAccountTransaction;
use App\Http\Middleware\SetDatesGestor;
use App\Http\Middleware\SetPlantoesIdsGestor;
use App\Http\Middleware\SetRegionalIdGestor;
use App\Http\Middleware\TreinamentoUser;
use App\Http\Middleware\TreinamentoUserHasAcesso;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Illuminate\Routing\Middleware\ThrottleRequests;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            'add-accept-json-header',
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api_service' => [
            'add-accept-json-header',
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            //            'check-ip-service',
            'check-service-email-cms',
        ],

        'api_hub' => [
            'add-accept-json-header',
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            'check-service-role:hub',
        ],

        'api_plantao' => [
            'add-accept-json-header',
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api_mop' => [
            'add-accept-json-header',
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api_salesforce' => [
            'add-accept-json-header',
            \Illuminate\Routing\Middleware\ThrottleRequests::class . ':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's middleware aliases.
     *
     * Aliases may be used to conveniently assign middleware to routes and groups.
     *
     * @var array
     */
    protected $middlewareAliases = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        //        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'precognitive' => \Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'add-accept-json-header' => AddAcceptJsonHeader::class,
        'check-ip-service' => CheckIpService::class,
        'check-service-email-cms' => CheckServiceEmailCms::class,
        'check-service-role' => CheckServiceRole::class,
        'edit-user-gestor-perfil' => EditUserGestorPerfil::class,
        'perfil-gestor' => CheckPerfilGestor::class,
        'set-regional-id-gestor' => SetRegionalIdGestor::class,
        'set-plantoes-ids-gestor' => SetPlantoesIdsGestor::class,
        'set-dates-gestor' => SetDatesGestor::class,

        'set-treinamento-user' => TreinamentoUser::class,
        'check-access-treinamento-user' => TreinamentoUserHasAcesso::class,

        'api-mop-filters-gestor' => ApiMopFiltersGestor::class,

        'check-corretor-aprovado' => CheckCorretorAprovado::class,
        'check-gestor' => EnsureGestorHasPermission::class,
        'check-salesforce-token' => CheckSalesforceToken::class,

        'sanctum-abilities' => \Laravel\Sanctum\Http\Middleware\CheckAbilities::class,
        'sanctum-ability' => \Laravel\Sanctum\Http\Middleware\CheckForAnyAbility::class,

        'cliente-conta-cliente' => CheckClienteContaCliente::class,
        'check-bolsao-access' => CheckBolsaoAccess::class,

        'has-bank-account' => HasBankAccount::class,
        'has-active-bank-account' => HasActiveBankAccount::class,
        'bank-is-enabled' => BankIsEnabled::class,
        'has-bank-account-transaction' => HasBankAccountTransaction::class,
    ];

    /* @inheritdoc */
    protected $middlewarePriority = [
        ThrottleRequests::class,
        AddAcceptJsonHeader::class,
    ];
}
