<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HasBankAccountTransaction
{
    /**
     * Handle an incoming request.
     *
     */
    public function handle(Request $request, Closure $next): Response
    {
        $requestControlKey = $request->route('requestControlKey');
        
        if (!$requestControlKey) {
            abort(422, 'Request control key é obrigatório');
        }

        $transaction = $request->bankAccount
            ->transactions()
            ->where('request_control_key', $requestControlKey)
            ->first();

        if (!$transaction) {
            abort(422, 'Transação não encontrada para esta conta');
        }

        $request->merge([
            'bankAccountTransaction' => $transaction
        ]);

        return $next($request);
    }
}
