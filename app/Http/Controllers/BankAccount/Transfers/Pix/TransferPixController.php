<?php

declare(strict_types=1);

namespace App\Http\Controllers\BankAccount\Transfers\Pix;

use App\Enums\BankAccount\Transactions\Pix\PixTransferTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\BankAccount\Transfers\Pix\AddFavoritePixTransferRequest;
use App\Http\Requests\BankAccount\Transfers\Pix\ConfirmPixTransferRequest;
use App\Http\Requests\BankAccount\Transfers\Pix\ExecutePixTransferRequest;
use App\Http\Requests\BankAccount\Transfers\Pix\ManualPixTransferRequest;
use App\Http\Resources\BankAccount\Transfers\Pix\ManualPixStatusResource;
use App\Http\Resources\BankAccount\Transfers\Pix\ManualPixTransferResource;
use App\Services\BankAccount\Transfers\Pix\PixTransferStrategyService;
use App\Models\BankAccount;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Arr;

class TransferPixController extends Controller
{
    public function __construct(
        private readonly PixTransferStrategyService $pixTransferStrategyService
    ) {
    }

    // Centraliza a obtenção da conta bancária do request, facilitando manutenção e testes futuros.
    private function getBankAccount(Request $request): BankAccount
    {
        return $request->bankAccount;
    }

    // Cria uma transferência PIX manual usando dados validados do request.
    public function createManualTransfer(ManualPixTransferRequest $request): JsonResponse
    {
        $bankAccount = $this->getBankAccount($request);

        $transferData = $request->validated();
        $transferData['bank_account_id'] = $bankAccount->id;
        $transferData['user_id'] = $bankAccount->user_id;
        $transferData['account_key'] = Arr::get($bankAccount, 'account_info.account_key');

        $transaction = $this->pixTransferStrategyService->createTransfer(
            PixTransferTypeEnum::MANUAL,
            $transferData
        );

        return response()->json(new ManualPixTransferResource($transaction));
    }

    // Executa uma transferência PIX após validação dos dados.
    public function executeTransfer(ExecutePixTransferRequest $request, string $requestControlKey): JsonResponse
    {
        $executionData = $request->validated();
        $executionData['request_control_key'] = $requestControlKey;

        $bankAccount = $this->bankAccount;
        $executionData['approver_document_number'] = $bankAccount->user->cpf;

        $this->pixTransferStrategyService->executeTransfer($executionData);

        return response()->json([], 200);
    }

    // Confirma uma transferência PIX usando dados validados e chave de controle.
    public function confirmTransfer(ConfirmPixTransferRequest $request, string $requestControlKey): JsonResponse
    {
        $confirmationData = $request->validated();
        $bankAccount = $this->bankAccount;
        $confirmationData['account_key'] = $bankAccount->account_info['account_key'];
        $confirmationData['request_control_key'] = $requestControlKey;

        $this->pixTransferStrategyService->confirmTransfer($confirmationData);

        return response()->json([], 200);
    }

    // Consulta o status de uma transferência PIX.
    public function statusTransfer(Request $request, string $requestControlKey): JsonResponse
    {
        $statusData['request_control_key'] = $requestControlKey;

        $status = $this->pixTransferStrategyService->statusTransfer($statusData);

        return response()->json(new ManualPixStatusResource($status));
    }

    // Retorna o comprovante de uma transferência PIX.
    public function receiptTransfer(Request $request, string $transaction_key): JsonResponse
    {
        $receipt = $this->pixTransferStrategyService->receiptTransfer($transaction_key);

        return response()->json($receipt);
    }

    // Adiciona uma transferência PIX aos favoritos (placeholder).
    public function addToFavorites(AddFavoritePixTransferRequest $request, string $requestControlKey): JsonResponse
    {
        //TODO
        // TRATAR ADD FAVORITE
        $bankAccount = $this->bankAccount;
        $user_id = $bankAccount->user_id;
        try {
            return response()->json([], 200);

        } catch (\Exception $e) {
            Log::error('Error adding favorite transfer', [
                'error' => $e->getMessage(),
                'request_control_key' => $requestControlKey,
                'user_id' => $user_id,
            ]);

            return response()->json([
                'message' => 'Erro ao adicionar transferência aos favoritos',
            ], 500);
        }
    }

}
