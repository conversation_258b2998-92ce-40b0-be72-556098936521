<?php

declare(strict_types=1);

namespace Tests\Unit\Rules;

use App\Rules\CpfCnpj;
use Illuminate\Contracts\Validation\ValidationRule;
use PHPUnit\Framework\TestCase;

class CpfCnpjTest extends TestCase
{
    private CpfCnpj $rule;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = new CpfCnpj();
    }

    public function test_rule_implements_validation_rule_interface(): void
    {
        $this->assertInstanceOf(ValidationRule::class, $this->rule);
    }

    public function test_validates_cpf_correctly(): void
    {
        // CPF válido: 529.982.247-25
        $validCpf = '52998224725';
        $this->assertTrue($this->rule->passes('document', $validCpf));

        // CPF inválido: 111.111.111-11
        $invalidCpf = '11111111111';
        $this->assertFalse($this->rule->passes('document', $invalidCpf));

        // CPF inválido: 123.456.789-00
        $invalidCpf2 = '12345678900';
        $this->assertFalse($this->rule->passes('document', $invalidCpf2));
    }

    public function test_validates_cnpj_correctly(): void
    {
        // CNPJ válido: 11.222.333/0001-81
        $validCnpj = '11222333000181';
        $this->assertTrue($this->rule->passes('document', $validCnpj));

        // CNPJ inválido: 11.111.111/1111-11
        $invalidCnpj = '11111111111111';
        $this->assertFalse($this->rule->passes('document', $invalidCnpj));

        // CNPJ inválido: 12.345.678/9012-34
        $invalidCnpj2 = '12345678901234';
        $this->assertFalse($this->rule->passes('document', $invalidCnpj2));
    }

    public function test_handles_formatted_documents(): void
    {
        // CPF com formatação
        $formattedCpf = '529.982.247-25';
        $this->assertTrue($this->rule->passes('document', $formattedCpf));

        // CNPJ com formatação
        $formattedCnpj = '11.222.333/0001-81';
        $this->assertTrue($this->rule->passes('document', $formattedCnpj));
    }

    public function test_rejects_invalid_lengths(): void
    {
        // Documento com 10 dígitos (nem CPF nem CNPJ)
        $invalidLength = '1234567890';
        $this->assertFalse($this->rule->passes('document', $invalidLength));

        // Documento com 12 dígitos (nem CPF nem CNPJ)
        $invalidLength2 = '123456789012';
        $this->assertFalse($this->rule->passes('document', $invalidLength2));

        // Documento com 15 dígitos (nem CPF nem CNPJ)
        $invalidLength3 = '123456789012345';
        $this->assertFalse($this->rule->passes('document', $invalidLength3));
    }

    public function test_handles_empty_or_null_values(): void
    {
        $this->assertFalse($this->rule->passes('document', ''));
        $this->assertFalse($this->rule->passes('document', null));
    }

    public function test_handles_non_numeric_characters(): void
    {
        // CPF com letras (deve ser válido após limpeza)
        $cpfWithLetters = '529.982.247-25';
        $this->assertTrue($this->rule->passes('document', $cpfWithLetters));

        // CNPJ com caracteres especiais (deve ser válido após limpeza)
        $cnpjWithSpecialChars = '11.222.333/0001-81';
        $this->assertTrue($this->rule->passes('document', $cnpjWithSpecialChars));

        // CPF com formatação e espaços
        $cpfWithFormatting = ' 529.982.247-25 ';
        $this->assertTrue($this->rule->passes('document', $cpfWithFormatting));
    }

    public function test_returns_correct_error_message(): void
    {
        $expectedMessage = 'O documento informado não é um CPF ou CNPJ válido.';
        $this->assertEquals($expectedMessage, $this->rule->message());
    }
}
