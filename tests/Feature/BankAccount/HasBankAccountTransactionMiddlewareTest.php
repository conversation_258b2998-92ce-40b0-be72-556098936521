<?php

declare(strict_types=1);

namespace Tests\Feature\BankAccount;

use App\Models\BankAccountTransaction;
use Tests\TestCase;

class HasBankAccountTransactionMiddlewareTest extends TestCase
{
    public function test_middleware_passes_with_valid_request_control_key(): void
    {
        $user = $this->getCorretorLogadoWithBankAccount();
        
        $transaction = BankAccountTransaction::factory()
            ->bankAccount($user->bankAccount)
            ->byManual()
            ->create();

        $response = $this->post("/app/bank-account/transfers/pix/{$transaction->request_control_key}", [
            'amount' => 100.50,
            'pix_message' => 'Test transfer',
            'latitude' => -23.5505,
            'longitude' => -46.6333,
            'password' => 'password',
            'tfa_type' => 'sms',
        ]);

        // Should not return 404 or 400, meaning middleware passed
        $this->assertNotEquals(404, $response->getStatusCode());
        $this->assertNotEquals(400, $response->getStatusCode());
    }

    public function test_middleware_fails_with_invalid_request_control_key(): void
    {
        $this->getCorretorLogadoWithBankAccount();

        $response = $this->post('/app/bank-account/transfers/pix/invalid-key', [
            'amount' => 100.50,
            'pix_message' => 'Test transfer',
            'latitude' => -23.5505,
            'longitude' => -46.6333,
            'password' => 'password',
            'tfa_type' => 'sms',
        ]);

        $response->assertStatus(422)
            ->assertJson(['message' => 'Transação não encontrada para esta conta']);
    }

    public function test_middleware_fails_with_transaction_from_different_bank_account(): void
    {
        $user1 = $this->getCorretorLogadoWithBankAccount();
        $user2 = $this->getCorretorLogadoWithBankAccount();
        
        $transaction = BankAccountTransaction::factory()
            ->bankAccount($user2->bankAccount)
            ->byManual()
            ->create();
        // Login as user1 but try to access user2's transaction
        $this->addAuthorizationHeader($user1);

        $response = $this->post("/app/bank-account/transfers/pix/{$transaction->request_control_key}", [
            'amount' => 100.50,
            'pix_message' => 'Pagamento teste',
            'latitude' => -23.5505,
            'longitude' => -46.6333,        
            'password' => '*********',
            'tfa_type' => 'sms',
        ]);

        $response->assertStatus(422)
            ->assertJson(['message' => 'A senha informada está incorreta.']);
    }

    public function test_middleware_works_with_status_endpoint(): void
    {
        $user = $this->getCorretorLogadoWithBankAccount();
        
        $transaction = BankAccountTransaction::factory()
            ->bankAccount($user->bankAccount)
            ->byManualWithAmount()
            ->sent()
            ->create();

        $response = $this->getJson("/app/bank-account/transfers/pix/{$transaction->request_control_key}/status");

        $this->assertNotEquals(404, $response->getStatusCode());
        $this->assertNotEquals(400, $response->getStatusCode());
    }

    public function test_middleware_works_with_confirm_endpoint(): void
    {
        $user = $this->getCorretorLogadoWithBankAccount();
        
        $transaction = BankAccountTransaction::factory()
            ->bankAccount($user->bankAccount)
            ->byManualWithAmount()
            ->create();

        $response = $this->post("/app/bank-account/transfers/pix/{$transaction->request_control_key}/confirm", [
            'tfa_type' => 'device',
        ]);

        $this->assertNotEquals(404, $response->getStatusCode());
        $this->assertNotEquals(400, $response->getStatusCode());
    }

    public function test_middleware_works_with_receipt_endpoint(): void
    {
        $user = $this->getCorretorLogadoWithBankAccount();
        
        $transaction = BankAccountTransaction::factory()
            ->bankAccount($user->bankAccount)
            ->byManualWithAmount()
            ->sent()
            ->create();

        $response = $this->getJson("/app/bank-account/transfers/pix/{$transaction->request_control_key}/receipt");

        $this->assertNotEquals(404, $response->getStatusCode());
        $this->assertNotEquals(400, $response->getStatusCode());
    }

    public function test_middleware_works_with_add_favorite_endpoint(): void
    {
        $user = $this->getCorretorLogadoWithBankAccount();
        
        $transaction = BankAccountTransaction::factory()
            ->bankAccount($user->bankAccount)
            ->byManualWithAmount()
            ->sent()
            ->create();

        $response = $this->getJson("/app/bank-account/transfers/pix/{$transaction->request_control_key}/add-favorite?name=Meu Favorito");

        $this->assertNotEquals(404, $response->getStatusCode());
        $this->assertNotEquals(400, $response->getStatusCode());
    }

    public function test_middleware_does_not_affect_manual_endpoint(): void
    {
        $user = $this->getCorretorLogadoWithBankAccount();

        $payload = [
            'ispb' => '********',
            'account_branch' => '0001',
            'account_number' => '123456',
            'account_digit' => '7',
            'account_type' => 'checking_account',
            'name' => 'João Silva',
            'document_number' => '***********',
        ];

        $response = $this->post('/app/bank-account/transfers/pix/manual', $payload);

        // Manual endpoint should work normally (not affected by middleware)
        $response->assertStatus(422);
    }
}
